#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Training SentencePiece models for the source and target
# Command: python3 train.py <train_source_file_tok> <train_target_file_tok>


import sys
import sentencepiece as spm

import os


outpath = ""    # change the path if needed 输出文件路径

# 遍历所有文件夹
def get_all_files(root_dir):
    file_list = []
    for dirpath, dirnames, filenames in os.walk(root_dir):
        for filename in filenames:
            full_path = os.path.join(dirpath, filename)
            file_list.append(full_path)
    return file_list

# 打开zh 和en数据集路径文件 存取成list
def get_file_list(inp_list_txt):
    file_list = []
    with open(inp_list_txt, "r", encoding="utf-8") as f:
        for line in f:
            file_list.append(line.strip())  # 去除每行末尾的换行符
    return file_list

inp_zh_list = '/home/<USER>/userdata/0518trans/OnMT0529/preprocess/parallel_ch.txt'
inp_en_list = '/home/<USER>/userdata/0518trans/OnMT0529/preprocess/parallel_en.txt'

train_source_file_tok = ",".join(get_file_list(inp_zh_list))
train_target_file_tok = ",".join(get_file_list(inp_en_list))

print(type(train_source_file_tok))

# train sentencepiece model from the source and target files
# and create `source/target.model` and `source/target.vocab`
# `source/target.vocab` is just a reference, not used in the segmentation.

# if the training data is too small and the maximum pieces reserved is less than 4000.
# decrease --vocab size or --hard_vocab_limit=false, which automatically shrink the vocab size.


# Source subword model

source_train_value = f'--input={train_source_file_tok} --model_prefix=./parallel_1/source --vocab_size=50000 --hard_vocab_limit=false --split_digits=true'
spm.SentencePieceTrainer.train(source_train_value)
print("Done, training a SentencepPiece model for the Source finished successfully!")


# Target subword model

target_train_value = f'--input={train_target_file_tok} --model_prefix=./parallel_1/target --vocab_size=50000 --hard_vocab_limit=false --split_digits=true'
spm.SentencePieceTrainer.train(target_train_value)
print("Done, training a SentencepPiece model for the Target finished successfully!")

# if __name__ == '__main__':
#     import argparse
#     parser = argparse.ArgumentParser()
#     parser.add_argument('--parallel_path', type = str, default = '/home/<USER>/userdata/0518trans/CCMT_WMT2023_chen/train/parallel', help = '平行数据集路径')
#     parser.add_argument('--check_file', type = str, default = 'parallel_all_files.txt', help = '输出所有文件路径 检查txt') # 目标文件在源文件下 ./filter中 用os加载
#     parser.add_argument('--source_lan', type = str, default = 'zh', help = '源语言')
#     parser.add_argument('--target_lan', type = str, default = 'en', help = '目标语言') 

#     args = parser.parse_args()

#     all_files = get_all_files(args.parallel_path)
    
#     # 写入到txt文件
#     with open(args.check_file, "w", encoding="utf-8") as f:
#         for file_path in all_files:
#             f.write(file_path + "\n")

#     print(f"共找到 {len(all_files)} 个文件，已写入 {args.check_file}")


    
