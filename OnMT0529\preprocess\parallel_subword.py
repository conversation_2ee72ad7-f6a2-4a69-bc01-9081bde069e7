#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Subwording the source and target files
# Command: python3 subword.py <sp_source_model_path> <sp_target_model_path> <source_file_path> <target_file_path>


import sys
import sentencepiece as spm

# 打开zh和en数据集路径文件 存取成list
def get_file_list(inp_list_txt):
    file_list = []
    with open(inp_list_txt, "r", encoding="utf-8") as f:
        for line in f:
            file_list.append(line.strip())  # 去除每行末尾的换行符
    return file_list


source_model = './parallel_1/source.model'
target_model = './parallel_1/target.model'
inp_zh_list = './parallel_1/parallel_ch.txt'
inp_en_list = './parallel_1/parallel_en.txt'
source_raw_list = get_file_list(inp_zh_list)
target_raw_list = get_file_list(inp_en_list)
source_subworded = './parallel_1/source_zh.subword'
target_subworded = './parallel_1/target_en.subword'

print("Source Model:", source_model)
print("Target Model:", target_model)
# print("Source Dataset:", source_raw)
# print("Target Dataset:", target_raw)


sp = spm.SentencePieceProcessor()


# Subwording the train source

sp.load(source_model)

with open(source_subworded, "w", encoding='utf-8') as source_subword:
    for i in range(len(source_raw_list)):
        with open(source_raw_list[i], encoding='utf-8') as source:
            for line in source:
                line = line.strip()
                line = sp.encode_as_pieces(line)
                line = " ".join(line)
                source_subword.write(line + "\n")
            print("Done subwording the source file!", source_raw_list[i])

print("Done subwording the source file! Output:", source_subworded)


# Subwording the train target

sp.load(target_model)

with open(target_subworded, "w", encoding='utf-8') as target_subword:
    for i in range(len(target_raw_list)):
        with open(target_raw_list[i], encoding='utf-8') as source:
            for line in source:
                line = line.strip()
                line = sp.encode_as_pieces(line)
                line = " ".join(line)
                target_subword.write(line + "\n")
            print("Done subwording the target file!", target_raw_list[i])

print("Done subwording the target file! Output:", target_subworded)

